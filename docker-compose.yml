version: "3.3"

volumes:
    database:
    database_test:
    data01:

services:
    phpmyadmin:
       image: phpmyadmin:latest


    click-and-buy:
        container_name: click-and-buy
        image: gitlab.lateos.net:5050/click_and_buy/click-and-buy/php:8.1
        working_dir: /var/www/app
        user: '1000'
        env_file:
            - .env
        volumes:
            - ./marketplace:/var/www/app
            - ./docker/config/php/local.ini:/usr/local/etc/php/conf.d/local.ini
        depends_on:
            - maria-db
            - redis
            - webpack-encore
            - mailcatcher
            - rabbitmq
        networks:
            - default

    izberg-offers-sync:
        container_name: izberg-offers-sync
        image: gitlab.lateos.net:5050/click_and_buy/click-and-buy/php:8.1
        user: '1000'
        working_dir: /var/www/izberg-offers-sync
        env_file:
            - .env
        volumes:
            - ./izberg-offers-sync:/var/www/izberg-offers-sync
        depends_on:
            - rabbitmq
            - elasticsearch
        networks:
            - default
        ulimits:
            nofile:
                soft: 65536
                hard: 65536

    izberg-offers-sync-consumer:
        container_name: izberg-offers-sync-consumer
        image: gitlab.lateos.net:5050/click_and_buy/click-and-buy/php:8.1
        user: '1000'
        working_dir: /var/www/izberg-offers-sync
        env_file:
            - .env
        volumes:
            - ./izberg-offers-sync:/var/www/izberg-offers-sync
        depends_on:
            - rabbitmq
            - elasticsearch
            - izberg-offers-sync
        networks:
            - default
        restart: on-failure
        command: php bin/console messenger:consume async

    webpack-encore:
        container_name: webpack-encore
        image: gitlab.lateos.net:5050/click_and_buy/click-and-buy/webpack:latest
        user: 'node'
        volumes:
            - ./docker/config/webpack-encore/.npmrc:/home/<USER>/.npmrc
            - ./marketplace:/app
            - ./messager:/messager
            - ./:/click-and-buy

    mailcatcher:
        container_name: mailcatcher
        image: gitlab.lateos.net:5050/click_and_buy/click-and-buy/mailcatcher:latest
        ports:
            - '1080:1080'
        networks:
            - default

    nginx:
        container_name: nginxed
        image: nginx
        restart: on-failure
        ports:
            - "80:80"
            - "443:443"
        volumes:
            - ./marketplace:/var/www/app
            - ./docker/config/nginx/conf.d/:/etc/nginx/conf.d/
            - ./docker/config/nginx/certs:/etc/nginx/certs
        networks:
            - default
        depends_on:
            - click-and-buy

    maria-db:
        container_name: maria-dbed
        image: mariadb:10.4.13
        ports:
            - "3306:3306"
        env_file:
            - .env
        volumes:
            - database:/var/lib/mysql
        networks:
            - default

    rabbitmq:
        container_name: rabbitmqed
        image: 'rabbitmq:3-management'
        ports:
            - "15672:15672"
        networks:
            - default
        healthcheck:
            test: ["CMD", "rabbitmqctl", "status"]
            interval: 5s
            timeout: 10s
            retries: 10

    elasticsearch:
        container_name: elasticsearched
        image: 'elasticsearch:7.9.3'
        environment:
            - node.name=es01
            - cluster.name=es-docker-cluster
            - cluster.initial_master_nodes=es01
            - bootstrap.memory_lock=true
            - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
        ulimits:
            memlock:
                soft: -1
                hard: -1
            nofile:
                soft: 65536
                hard: 65536
        ports:
            - "9200:9200"
        volumes:
            - data01:/usr/share/elasticsearch/data
        networks:
            - default

#    kibana:
#        container_name: kibana
#        image: kibana:7.9.3
#        ports:
#            - '5601:5601'
#        depends_on:
#            - elasticsearch
#        networks:
#            - default

    redis:
        container_name: redised
        image: redis
        networks:
            - default

#    blackfire:
#        container_name: blackfire
#        image: blackfire/blackfire
#        ports: [ "8707" ]
#        env_file:
#            - .env